
> frontend@0.1.0 dev
> next dev

   ▲ Next.js 15.5.4
   - Local:        http://localhost:3000
   - Network:      http://************:3000

 ✓ Starting...
 ✓ Ready in 2.1s
 ○ Compiling / ...
 ✓ Compiled / in 2.4s (1434 modules)
 GET / 200 in 3199ms
 GET / 200 in 136ms
 ⨯ ./src/app/page.tsx
Error:   [31mx[0m Expression expected
    ,-[[36;1;4mC:\dev\nurtured-care-afh\apps\frontend\src\app\page.tsx[0m:8:1]
 [2m 5[0m |       <>
 [2m 6[0m |         <Navbar />
 [2m 7[0m |         <HeroBanner
 [2m 8[0m |       </>
    : [35;1m      ^[0m
 [2m 9[0m |       // <main style={{ display: "flex", height: "100vh", alignItems: "center", justifyContent: "center" }}>
 [2m10[0m |       //   <h1>Hello from Next.js + TypeScript 👋</h1>
 [2m10[0m |       // </main>
    `----
  [31mx[0m Unterminated regexp literal
    ,-[[36;1;4mC:\dev\nurtured-care-afh\apps\frontend\src\app\page.tsx[0m:8:1]
 [2m 5[0m |       <>
 [2m 6[0m |         <Navbar />
 [2m 7[0m |         <HeroBanner
 [2m 8[0m |       </>
    : [35;1m       ^^[0m
 [2m 9[0m |       // <main style={{ display: "flex", height: "100vh", alignItems: "center", justifyContent: "center" }}>
 [2m10[0m |       //   <h1>Hello from Next.js + TypeScript 👋</h1>
 [2m10[0m |       // </main>
    `----

Caused by:
    Syntax Error

Import trace for requested module:
./src/app/page.tsx
 ○ Compiling /_error ...
 ⨯ ./src/app/page.tsx
Error:   [31mx[0m Expression expected
    ,-[[36;1;4mC:\dev\nurtured-care-afh\apps\frontend\src\app\page.tsx[0m:8:1]
 [2m 5[0m |       <>
 [2m 6[0m |         <Navbar />
 [2m 7[0m |         <HeroBanner
 [2m 8[0m |       </>
    : [35;1m      ^[0m
 [2m 9[0m |       // <main style={{ display: "flex", height: "100vh", alignItems: "center", justifyContent: "center" }}>
 [2m10[0m |       //   <h1>Hello from Next.js + TypeScript 👋</h1>
 [2m10[0m |       // </main>
    `----
  [31mx[0m Unterminated regexp literal
    ,-[[36;1;4mC:\dev\nurtured-care-afh\apps\frontend\src\app\page.tsx[0m:8:1]
 [2m 5[0m |       <>
 [2m 6[0m |         <Navbar />
 [2m 7[0m |         <HeroBanner
 [2m 8[0m |       </>
    : [35;1m       ^^[0m
 [2m 9[0m |       // <main style={{ display: "flex", height: "100vh", alignItems: "center", justifyContent: "center" }}>
 [2m10[0m |       //   <h1>Hello from Next.js + TypeScript 👋</h1>
 [2m10[0m |       // </main>
    `----

Caused by:
    Syntax Error

Import trace for requested module:
./src/app/page.tsx
 GET / 500 in 1725ms
 GET / 500 in 186ms
 GET / 500 in 35ms
 GET / 500 in 29ms
 ⚠ Fast Refresh had to perform a full reload due to a runtime error.
 ⚠ Fast Refresh had to perform a full reload due to a runtime error.
 ✓ Compiled /_error in 1443ms (1774 modules)
 GET / 200 in 235ms
 ⨯ ReferenceError: HeroBanner is not defined
    at Home (src\app\page.tsx:7:10)
   5 |       <>
   6 |         <Navbar />
>  7 |         <HeroBanner />
     |          ^
   8 |       </>
   9 |       // <main style={{ display: "flex", height: "100vh", alignItems: "center", justifyContent: "center" }}>
  10 |       //   <h1>Hello from Next.js + TypeScript 👋</h1> {
  digest: '1146266501'
}
 ⨯ ReferenceError: HeroBanner is not defined
    at Home (src\app\page.tsx:7:10)
   5 |       <>
   6 |         <Navbar />
>  7 |         <HeroBanner />
     |          ^
   8 |       </>
   9 |       // <main style={{ display: "flex", height: "100vh", alignItems: "center", justifyContent: "center" }}>
  10 |       //   <h1>Hello from Next.js + TypeScript 👋</h1> {
  digest: '1146266501'
}
 ⨯ ReferenceError: HeroBanner is not defined
    at Home (src\app\page.tsx:7:10)
   5 |       <>
   6 |         <Navbar />
>  7 |         <HeroBanner />
     |          ^
   8 |       </>
   9 |       // <main style={{ display: "flex", height: "100vh", alignItems: "center", justifyContent: "center" }}>
  10 |       //   <h1>Hello from Next.js + TypeScript 👋</h1> {
  digest: '1146266501'
}
 GET / 500 in 680ms
 ✓ Compiled in 266ms (806 modules)
 ⨯ ReferenceError: HeroBanner is not defined
    at Home (src\app\page.tsx:7:10)
   5 |       <>
   6 |         <Navbar />
>  7 |         <HeroBanner />
     |          ^
   8 |       </>
   9 |       // <main style={{ display: "flex", height: "100vh", alignItems: "center", justifyContent: "center" }}>
  10 |       //   <h1>Hello from Next.js + TypeScript 👋</h1> {
  digest: '1146266501'
}
 GET / 500 in 201ms
 ✓ Compiled in 553ms (1775 modules)
 GET / 200 in 670ms
 ✓ Compiled in 730ms (1775 modules)
 GET / 200 in 159ms
 ○ Compiling /_not-found ...
 ✓ Compiled /_not-found in 783ms (1766 modules)
 GET /image/home-bg.png 404 in 1259ms
 GET / 200 in 179ms
 GET /icons/nurtured-care-logo.png 404 in 97ms
 GET /image/home-bg.png 404 in 276ms
 ⨯ The requested resource isn't a valid image for /icons/nurtured-care-logo.png received null
[Error: The requested resource isn't a valid image.] { statusCode: 400 }
 ✓ Compiled in 533ms (1780 modules)
 GET / 200 in 138ms
 GET /image/home-bg.png 404 in 159ms
 ✓ Compiled in 354ms (1780 modules)
 GET / 200 in 96ms
 GET /image/home-bg.png 404 in 167ms
 ✓ Compiled in 364ms (1780 modules)
 GET / 200 in 110ms
 GET /image/home-bg.png 404 in 163ms
<w> [webpack.cache.PackFileCacheStrategy] Caching failed for pack: Error: EPERM: operation not permitted, rename 'C:\dev\nurtured-care-afh\apps\frontend\.next\cache\webpack\server-development\7.pack.gz_' -> 'C:\dev\nurtured-care-afh\apps\frontend\.next\cache\webpack\server-development\7.pack.gz'
 ✓ Compiled in 577ms (1775 modules)
 GET / 200 in 186ms
 ✓ Compiled /_not-found in 360ms (1766 modules)
 GET /image/home-bg.png 404 in 651ms
