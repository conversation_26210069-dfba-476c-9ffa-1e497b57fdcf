
> frontend@0.1.0 dev
> next dev

   ▲ Next.js 15.5.4
   - Local:        http://localhost:3000
   - Network:      http://************:3000

 ✓ Starting...
 ✓ Ready in 1904ms
 ○ Compiling / ...
 ⨯ ./src/components/Navbar.tsx
Error:   [31mx[0m You're importing a component that needs `useRef`. This React Hook only works in a Client Component. To fix, mark the file (or its parent) with the `"use client"` directive.
  [31m|[0m
  [31m|[0m  Learn more: https://nextjs.org/docs/app/api-reference/directives/use-client
  [31m|[0m

   ,-[[36;1;4mC:\dev\nurtured-care-afh\apps\frontend\src\components\Navbar.tsx[0m:4:1]
 [2m1[0m | import Link from "next/link";
 [2m2[0m | import Image from "next/image";
 [2m3[0m | import { Search } from "lucide-react";
 [2m4[0m | import { useRef } from "react";
   : [35;1m         ^^^^^^[0m
 [2m5[0m | 
 [2m6[0m | const Navbar = () => {
 [2m6[0m |   const searchInputRef = useRef<HTMLInputElement>(null);
   `----

Import trace for requested module:
./src/components/Navbar.tsx
./src/app/page.tsx
 ⨯ ./src/components/Navbar.tsx
Error:   [31mx[0m You're importing a component that needs `useRef`. This React Hook only works in a Client Component. To fix, mark the file (or its parent) with the `"use client"` directive.
  [31m|[0m
  [31m|[0m  Learn more: https://nextjs.org/docs/app/api-reference/directives/use-client
  [31m|[0m

   ,-[[36;1;4mC:\dev\nurtured-care-afh\apps\frontend\src\components\Navbar.tsx[0m:4:1]
 [2m1[0m | import Link from "next/link";
 [2m2[0m | import Image from "next/image";
 [2m3[0m | import { Search } from "lucide-react";
 [2m4[0m | import { useRef } from "react";
   : [35;1m         ^^^^^^[0m
 [2m5[0m | 
 [2m6[0m | const Navbar = () => {
 [2m6[0m |   const searchInputRef = useRef<HTMLInputElement>(null);
   `----

Import trace for requested module:
./src/components/Navbar.tsx
./src/app/page.tsx
 ⨯ ./src/components/Navbar.tsx
Error:   [31mx[0m You're importing a component that needs `useRef`. This React Hook only works in a Client Component. To fix, mark the file (or its parent) with the `"use client"` directive.
  [31m|[0m
  [31m|[0m  Learn more: https://nextjs.org/docs/app/api-reference/directives/use-client
  [31m|[0m

   ,-[[36;1;4mC:\dev\nurtured-care-afh\apps\frontend\src\components\Navbar.tsx[0m:4:1]
 [2m1[0m | import Link from "next/link";
 [2m2[0m | import Image from "next/image";
 [2m3[0m | import { Search } from "lucide-react";
 [2m4[0m | import { useRef } from "react";
   : [35;1m         ^^^^^^[0m
 [2m5[0m | 
 [2m6[0m | const Navbar = () => {
 [2m6[0m |   const searchInputRef = useRef<HTMLInputElement>(null);
   `----

Import trace for requested module:
./src/components/Navbar.tsx
./src/app/page.tsx
 GET / 500 in 7551ms
 ⚠ Fast Refresh had to perform a full reload due to a runtime error.
 ⨯ ./src/components/Navbar.tsx
Error:   [31mx[0m You're importing a component that needs `useRef`. This React Hook only works in a Client Component. To fix, mark the file (or its parent) with the `"use client"` directive.
  [31m|[0m
  [31m|[0m  Learn more: https://nextjs.org/docs/app/api-reference/directives/use-client
  [31m|[0m

   ,-[[36;1;4mC:\dev\nurtured-care-afh\apps\frontend\src\components\Navbar.tsx[0m:4:1]
 [2m1[0m | import Link from "next/link";
 [2m2[0m | import Image from "next/image";
 [2m3[0m | import { Search } from "lucide-react";
 [2m4[0m | import React, { useRef } from "react";
   : [35;1m                ^^^^^^[0m
 [2m5[0m | 
 [2m6[0m | const Navbar = () => {
 [2m6[0m |   const searchInputRef = useRef<HTMLInputElement>(null);
   `----

Import trace for requested module:
./src/components/Navbar.tsx
./src/app/page.tsx
 ⨯ ./src/components/Navbar.tsx
Error:   [31mx[0m You're importing a component that needs `useRef`. This React Hook only works in a Client Component. To fix, mark the file (or its parent) with the `"use client"` directive.
  [31m|[0m
  [31m|[0m  Learn more: https://nextjs.org/docs/app/api-reference/directives/use-client
  [31m|[0m

   ,-[[36;1;4mC:\dev\nurtured-care-afh\apps\frontend\src\components\Navbar.tsx[0m:4:1]
 [2m1[0m | import Link from "next/link";
 [2m2[0m | import Image from "next/image";
 [2m3[0m | import { Search } from "lucide-react";
 [2m4[0m | import React, { useRef } from "react";
   : [35;1m                ^^^^^^[0m
 [2m5[0m | 
 [2m6[0m | const Navbar = () => {
 [2m6[0m |   const searchInputRef = useRef<HTMLInputElement>(null);
   `----

Import trace for requested module:
./src/components/Navbar.tsx
./src/app/page.tsx
 GET / 500 in 326ms
 ⚠ Fast Refresh had to perform a full reload due to a runtime error.
 ⚠ Fast Refresh had to perform a full reload due to a runtime error.
 ✓ Compiled /_error in 1219ms (919 modules)
 GET / 200 in 928ms
 ✓ Compiled in 389ms (919 modules)
 ✓ Compiled in 242ms (907 modules)
 ✓ Compiled in 384ms (902 modules)
 ✓ Compiled in 271ms (902 modules)
 ✓ Compiled in 210ms (902 modules)
 ✓ Compiled in 293ms (902 modules)
<w> [webpack.cache.PackFileCacheStrategy] Caching failed for pack: Error: EPERM: operation not permitted, rename 'C:\dev\nurtured-care-afh\apps\frontend\.next\cache\webpack\server-development\6.pack.gz_' -> 'C:\dev\nurtured-care-afh\apps\frontend\.next\cache\webpack\server-development\6.pack.gz'
 ✓ Compiled in 565ms (902 modules)
 ✓ Compiled in 289ms (902 modules)
 ✓ Compiled in 276ms (902 modules)
 ✓ Compiled in 172ms (902 modules)
 ✓ Compiled in 282ms (902 modules)
