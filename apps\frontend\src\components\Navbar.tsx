"use client";
import Link from "next/link";
import Image from "next/image";
import { Menu } from "lucide-react";
import React, { useState } from "react";
import { navbars } from "@/lib/data/navbar";
import { usePathname } from "next/navigation";

const Navbar = () => {
    const pathname = usePathname();
    const [isMobileMenuOpen, setIsMobileMenuOpen] = useState(false);

    const handleClick = () => {
        setIsMobileMenuOpen(!isMobileMenuOpen);
    };

  return (
    <nav className="bg-[#2a2e30] px-4 sm:px-6 md:px-8 lg:px-12 relative">
        <div className="hidden md:flex flex-row items-center justify-between p-4 gap-4">
            <div className="flex flex-row items-center gap-4">
                <Image
                src="/icons/nurtured-care-logo.png"
                alt="logo"
                width={75}
                height={50}
                />
                <div className="text-white font-[Poppins] text-xl font-bold">
                    Nurtured Care
                </div>
            </div>
            <div className="flex flex-row justify-center text-white gap-12">
                {navbars.map((navbar) => (
                    <Link
                        key={navbar.name}
                        href={navbar.href}
                        className={`${pathname == navbar.href ? 'underline underline-offset-6' : 'text-gray-600'} text-white hover:text-gray-300`}
                    >
                        {navbar.name}
                    </Link>
                ))}
            </div>
            <div className="flex cursor-pointer items-center justify-end bg-white rounded-full p-2.5">
                <button
                    className="relative text-gray-800"
                    onClick={() => {
                        // TODO: Implement request a tour functionality
                        console.log('Request a tour');
                    }}
                >
                    Request a tour
                </button>
            </div>
        </div>
        <div className="md:hidden flex flex-row items-center justify-between p-4 gap-4">
            <div className="flex flex-row items-center gap-4">
                <Image
                src="/icons/nurtured-care-logo.png"
                alt="logo"
                width={75}
                height={50}
                />
                <div className="text-white font-[Poppins] text-xl font-bold">
                    Nurtured Care
                </div>
            </div>
            <div>
                <Menu 
                    size={24} 
                    onClick={handleClick}/>
            </div>

            
        </div>
    </nav>
  );
};

export default Navbar;
