"use client";
import Link from "next/link";
import Image from "next/image";
import { Search } from "lucide-react";
import React, { useRef } from "react";

const Navbar = () => {
  const searchInputRef = useRef<HTMLInputElement>(null);

  const handleSearch = (e: React.FormEvent) => {
    e.preventDefault();
    const query = searchInputRef.current?.value;
    if (query) {
      // TODO: Implement search functionality
      console.log('Searching for:', query);
    }
  };

  return (
    <nav className="bg-white px-4 sm:px-6 md:px-8 lg:px-12 relative">
        <div className="flex flex-row justify-between p-4 gap-4">
            <div>
                <Image
                src="/icons/nurtured-care-logo.svg"
                alt="logo"
                width={70}
                height={32}
                />
            </div>
            <div className="flex-1 flex justify-center">

            <div className="w-full max-w-4xl flex items-center rounded-lg px-4 py-2">
              
              
            </div>
          </div>
        </div>
    </nav>
  );
};

export default Navbar;
