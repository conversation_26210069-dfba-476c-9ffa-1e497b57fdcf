"use client";
import Link from "next/link";
import Image from "next/image";
import { Menu, X } from "lucide-react";
import React, { useState } from "react";
import { navbars } from "@/lib/data/navbar";
import { usePathname } from "next/navigation";
import { motion, AnimatePresence } from "framer-motion";

const Navbar = () => {
    const pathname = usePathname();
    const [isMobileMenuOpen, setIsMobileMenuOpen] = useState(false);

    const handleClick = () => {
        setIsMobileMenuOpen(!isMobileMenuOpen);
    };

  return (
    <nav className="bg-[#2a2e30] px-4 sm:px-6 md:px-8 lg:px-12 relative">
        <div className="hidden md:flex flex-row items-center justify-between p-4 gap-4">
            <div className="flex flex-row items-center gap-4">
                <Image
                src="/icons/nurtured-care-logo.png"
                alt="logo"
                width={75}
                height={50}
                />
                <div className="text-white font-[Poppins] text-xl font-bold">
                    Nurtured Care
                </div>
            </div>
            <div className="flex flex-row justify-center text-white gap-12">
                {navbars.map((navbar) => (
                    <Link
                        key={navbar.name}
                        href={navbar.href}
                        className={`${pathname == navbar.href ? 'underline underline-offset-6' : 'text-gray-600'} text-white hover:text-gray-300`}
                    >
                        {navbar.name}
                    </Link>
                ))}
            </div>
            <div className="flex cursor-pointer items-center justify-end bg-white rounded-full p-2.5">
                <button
                    className="relative text-gray-800"
                    onClick={() => {
                        // TODO: Implement request a tour functionality
                        console.log('Request a tour');
                    }}
                >
                    Request a tour
                </button>
            </div>
        </div>
        {/* Mobile menu */}
        <div className="md:hidden flex flex-row items-center justify-between p-4 gap-4">
            <div className="flex flex-row items-center gap-4">
                <Image
                src="/icons/nurtured-care-logo.png"
                alt="logo"
                width={75}
                height={50}
                />
                <div className="text-white font-[Poppins] text-xl font-bold">
                    Nurtured Care
                </div>
            </div>
            <div>
                <Menu
                    size={35}
                    className="text-white cursor-pointer"
                    onClick={handleClick}
                />
            </div>
        </div>

        {/* Full-screen mobile menu overlay */}
        <motion.div
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            exit={{ opacity: 0 }}
            transition={{ duration: 0.3 }}
        >
            {isMobileMenuOpen && (
                <div className="fixed inset-0 bg-[#2a2e30] z-50 flex flex-col">
                    {/* Header with close button */}
                    <div className="flex flex-row items-center justify-between p-4">
                        <div className="flex flex-row items-center gap-4">
                            <Image
                                src="/icons/nurtured-care-logo.png"
                                alt="logo"
                                width={75}
                                height={50}
                            />
                            <div className="text-white font-[Poppins] text-xl font-bold">
                                Nurtured Care
                            </div>
                        </div>
                        <X
                            size={35}
                            className="text-white cursor-pointer"
                            onClick={handleClick}
                        />
                    </div>

                    {/* Menu items */}
                    <div className="flex-1 flex flex-col items-center justify-center gap-8 px-8">
                        {navbars.map((navbar) => (
                            <Link
                                key={navbar.name}
                                href={navbar.href}
                                className={`${
                                    pathname === navbar.href
                                        ? 'text-white border-b-2 border-white'
                                        : 'text-gray-300'
                                } text-2xl font-[Poppins] font-medium hover:text-white transition-colors duration-200`}
                                onClick={() => setIsMobileMenuOpen(false)}
                            >
                                {navbar.name}
                            </Link>
                        ))}

                        {/* Request a tour button */}
                        <button
                            className="mt-8 bg-white text-[#2a2e30] px-8 py-3 rounded-full font-[Poppins] font-medium text-lg hover:bg-gray-100 transition-colors duration-200"
                            onClick={() => {
                                setIsMobileMenuOpen(false);
                                // TODO: Implement request a tour functionality
                                console.log('Request a tour');
                            }}
                        >
                            Request a tour
                        </button>
                    </div>
                </div>
            )}
        </motion.div>
    </nav>
  );
};

export default Navbar;
