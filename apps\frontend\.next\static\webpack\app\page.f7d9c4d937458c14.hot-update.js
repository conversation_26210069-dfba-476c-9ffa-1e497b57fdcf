"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/page",{

/***/ "(app-pages-browser)/./src/components/Navbar.tsx":
/*!***********************************!*\
  !*** ./src/components/Navbar.tsx ***!
  \***********************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/../../node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/link */ \"(app-pages-browser)/../../node_modules/next/dist/client/app-dir/link.js\");\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(next_link__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_image__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/image */ \"(app-pages-browser)/../../node_modules/next/dist/api/image.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! react */ \"(app-pages-browser)/../../node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_3__);\n/* harmony import */ var _lib_data_navbar__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/lib/data/navbar */ \"(app-pages-browser)/./src/lib/data/navbar.ts\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\nconst Navbar = ()=>{\n    _s();\n    const searchInputRef = (0,react__WEBPACK_IMPORTED_MODULE_3__.useRef)(null);\n    const handleSearch = (e)=>{\n        var _searchInputRef_current;\n        e.preventDefault();\n        const query = (_searchInputRef_current = searchInputRef.current) === null || _searchInputRef_current === void 0 ? void 0 : _searchInputRef_current.value;\n        if (query) {\n            // TODO: Implement search functionality\n            console.log('Searching for:', query);\n        }\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"nav\", {\n        className: \"bg-[#2a2e30] px-4 sm:px-6 md:px-8 lg:px-12 relative\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"flex flex-row items-center justify-between p-4 gap-4\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex flex-row items-center gap-4\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_image__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                            src: \"/icons/nurtured-care-logo.png\",\n                            alt: \"logo\",\n                            width: 75,\n                            height: 50\n                        }, void 0, false, {\n                            fileName: \"C:\\\\dev\\\\nurtured-care-afh\\\\apps\\\\frontend\\\\src\\\\components\\\\Navbar.tsx\",\n                            lineNumber: 24,\n                            columnNumber: 17\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"text-white font-[Poppins] text-xl font-bold\",\n                            children: \"Nurtured Care\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\dev\\\\nurtured-care-afh\\\\apps\\\\frontend\\\\src\\\\components\\\\Navbar.tsx\",\n                            lineNumber: 30,\n                            columnNumber: 17\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\dev\\\\nurtured-care-afh\\\\apps\\\\frontend\\\\src\\\\components\\\\Navbar.tsx\",\n                    lineNumber: 23,\n                    columnNumber: 13\n                }, undefined),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex flex-row justify-center text-white gap-12\",\n                    children: _lib_data_navbar__WEBPACK_IMPORTED_MODULE_4__.navbars.map((navbar)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_1___default()), {\n                            href: navbar.href,\n                            className: \"hover:text-gray-600\",\n                            children: navbar.name\n                        }, navbar.name, false, {\n                            fileName: \"C:\\\\dev\\\\nurtured-care-afh\\\\apps\\\\frontend\\\\src\\\\components\\\\Navbar.tsx\",\n                            lineNumber: 36,\n                            columnNumber: 21\n                        }, undefined))\n                }, void 0, false, {\n                    fileName: \"C:\\\\dev\\\\nurtured-care-afh\\\\apps\\\\frontend\\\\src\\\\components\\\\Navbar.tsx\",\n                    lineNumber: 34,\n                    columnNumber: 13\n                }, undefined),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex bg-white justify-center items-center rounded-full p-2\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                        className: \"text-gray-800 items-center\",\n                        children: \"Request a tour\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\dev\\\\nurtured-care-afh\\\\apps\\\\frontend\\\\src\\\\components\\\\Navbar.tsx\",\n                        lineNumber: 46,\n                        columnNumber: 17\n                    }, undefined)\n                }, void 0, false, {\n                    fileName: \"C:\\\\dev\\\\nurtured-care-afh\\\\apps\\\\frontend\\\\src\\\\components\\\\Navbar.tsx\",\n                    lineNumber: 45,\n                    columnNumber: 13\n                }, undefined)\n            ]\n        }, void 0, true, {\n            fileName: \"C:\\\\dev\\\\nurtured-care-afh\\\\apps\\\\frontend\\\\src\\\\components\\\\Navbar.tsx\",\n            lineNumber: 22,\n            columnNumber: 9\n        }, undefined)\n    }, void 0, false, {\n        fileName: \"C:\\\\dev\\\\nurtured-care-afh\\\\apps\\\\frontend\\\\src\\\\components\\\\Navbar.tsx\",\n        lineNumber: 21,\n        columnNumber: 5\n    }, undefined);\n};\n_s(Navbar, \"1Huwt9f9sVQqX6JpiuuE0oS3qT8=\");\n_c = Navbar;\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (Navbar);\nvar _c;\n$RefreshReg$(_c, \"Navbar\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/Navbar.tsx\n"));

/***/ })

});