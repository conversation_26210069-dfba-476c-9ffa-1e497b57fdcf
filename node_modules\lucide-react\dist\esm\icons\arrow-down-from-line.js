/**
 * @license lucide-react v0.545.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */

import createLucideIcon from '../createLucideIcon.js';

const __iconNode = [
  ["path", { d: "M19 3H5", key: "1236rx" }],
  ["path", { d: "M12 21V7", key: "gj6g52" }],
  ["path", { d: "m6 15 6 6 6-6", key: "h15q88" }]
];
const ArrowDownFromLine = createLucideIcon("arrow-down-from-line", __iconNode);

export { __iconNode, ArrowDownFromLine as default };
//# sourceMappingURL=arrow-down-from-line.js.map
