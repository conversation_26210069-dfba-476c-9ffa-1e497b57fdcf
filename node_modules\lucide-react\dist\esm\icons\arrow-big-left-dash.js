/**
 * @license lucide-react v0.545.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */

import createLucideIcon from '../createLucideIcon.js';

const __iconNode = [
  [
    "path",
    {
      d: "M13 9a1 1 0 0 1-1-1V5.061a1 1 0 0 0-1.811-.75l-6.835 6.836a1.207 1.207 0 0 0 0 1.707l6.835 6.835a1 1 0 0 0 1.811-.75V16a1 1 0 0 1 1-1h2a1 1 0 0 0 1-1v-4a1 1 0 0 0-1-1z",
      key: "p8w4w5"
    }
  ],
  ["path", { d: "M20 9v6", key: "14roy0" }]
];
const ArrowBigLeftDash = createLucideIcon("arrow-big-left-dash", __iconNode);

export { __iconNode, ArrowBigLeftDash as default };
//# sourceMappingURL=arrow-big-left-dash.js.map
