{"c": ["app/layout", "app/page", "webpack"], "r": [], "m": ["(app-pages-browser)/../../node_modules/lucide-react/dist/esm/Icon.js", "(app-pages-browser)/../../node_modules/lucide-react/dist/esm/createLucideIcon.js", "(app-pages-browser)/../../node_modules/lucide-react/dist/esm/defaultAttributes.js", "(app-pages-browser)/../../node_modules/lucide-react/dist/esm/icons/menu.js", "(app-pages-browser)/../../node_modules/lucide-react/dist/esm/icons/x.js", "(app-pages-browser)/../../node_modules/lucide-react/dist/esm/shared/src/utils.js", "(app-pages-browser)/../../node_modules/next/dist/api/image.js", "(app-pages-browser)/../../node_modules/next/dist/api/navigation.js", "(app-pages-browser)/../../node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5Cdev%5C%5Cnurtured-care-afh%5C%5Capps%5C%5Cfrontend%5C%5Csrc%5C%5Ccomponents%5C%5CNavbar.tsx%22%2C%22ids%22%3A%5B%22default%22%5D%7D&server=false!", "(app-pages-browser)/../../node_modules/next/dist/client/app-dir/link.js", "(app-pages-browser)/../../node_modules/next/dist/client/image-component.js", "(app-pages-browser)/../../node_modules/next/dist/client/use-merged-ref.js", "(app-pages-browser)/../../node_modules/next/dist/compiled/picomatch/index.js", "(app-pages-browser)/../../node_modules/next/dist/compiled/react/cjs/react-jsx-dev-runtime.development.js", "(app-pages-browser)/../../node_modules/next/dist/compiled/react/jsx-dev-runtime.js", "(app-pages-browser)/../../node_modules/next/dist/shared/lib/amp-context.shared-runtime.js", "(app-pages-browser)/../../node_modules/next/dist/shared/lib/amp-mode.js", "(app-pages-browser)/../../node_modules/next/dist/shared/lib/get-img-props.js", "(app-pages-browser)/../../node_modules/next/dist/shared/lib/head.js", "(app-pages-browser)/../../node_modules/next/dist/shared/lib/image-blur-svg.js", "(app-pages-browser)/../../node_modules/next/dist/shared/lib/image-config-context.shared-runtime.js", "(app-pages-browser)/../../node_modules/next/dist/shared/lib/image-config.js", "(app-pages-browser)/../../node_modules/next/dist/shared/lib/image-external.js", "(app-pages-browser)/../../node_modules/next/dist/shared/lib/image-loader.js", "(app-pages-browser)/../../node_modules/next/dist/shared/lib/match-local-pattern.js", "(app-pages-browser)/../../node_modules/next/dist/shared/lib/match-remote-pattern.js", "(app-pages-browser)/../../node_modules/next/dist/shared/lib/router-context.shared-runtime.js", "(app-pages-browser)/../../node_modules/next/dist/shared/lib/router/utils/format-url.js", "(app-pages-browser)/../../node_modules/next/dist/shared/lib/router/utils/is-local-url.js", "(app-pages-browser)/../../node_modules/next/dist/shared/lib/router/utils/querystring.js", "(app-pages-browser)/../../node_modules/next/dist/shared/lib/side-effect.js", "(app-pages-browser)/../../node_modules/next/dist/shared/lib/utils.js", "(app-pages-browser)/../../node_modules/next/dist/shared/lib/utils/error-once.js", "(app-pages-browser)/./src/components/Navbar.tsx", "(app-pages-browser)/./src/lib/data/navbar.ts"]}