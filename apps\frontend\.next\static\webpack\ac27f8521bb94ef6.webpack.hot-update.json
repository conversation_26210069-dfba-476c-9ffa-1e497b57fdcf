{"c": ["app/page", "webpack"], "r": [], "m": ["(app-pages-browser)/../../node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5Cdev%5C%5Cnurtured-care-afh%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Capp-dir%5C%5Clink.js%22%2C%22ids%22%3A%5B%22__esModule%22%2C%22default%22%5D%7D&server=false!", "(app-pages-browser)/../../node_modules/next/dist/client/app-dir/link.js", "(app-pages-browser)/../../node_modules/next/dist/client/use-merged-ref.js", "(app-pages-browser)/../../node_modules/next/dist/shared/lib/router/utils/format-url.js", "(app-pages-browser)/../../node_modules/next/dist/shared/lib/router/utils/is-local-url.js", "(app-pages-browser)/../../node_modules/next/dist/shared/lib/router/utils/querystring.js", "(app-pages-browser)/../../node_modules/next/dist/shared/lib/utils.js", "(app-pages-browser)/../../node_modules/next/dist/shared/lib/utils/error-once.js"]}