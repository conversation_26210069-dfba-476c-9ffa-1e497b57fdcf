# Nurtured Care AFH

A modern Adult Family Home Management System built with Next.js and TypeScript in a monorepo architecture.

## 🏗️ Project Structure

This is a monorepo managed with [Turbo](https://turbo.build/) and npm workspaces:

```
nurtured-care-afh/
├── apps/
│   └── frontend/          # Next.js frontend application
├── packages/              # Shared packages (future)
├── package.json           # Root package.json with workspace configuration
├── turbo.json            # Turbo configuration
└── tsconfig.json         # Root TypeScript configuration
```

## 🚀 Getting Started

### Prerequisites

- Node.js 18+ 
- npm 10.8.1+ (specified in package.json)

### Installation

1. Clone the repository:
   ```bash
   git clone <repository-url>
   cd nurtured-care-afh
   ```

2. Install dependencies:
   ```bash
   npm install
   ```

### Development

Start the development server:

```bash
# Start all apps in parallel
npm run dev

# Start only the frontend
npm run dev:frontend
```

The frontend will be available at [http://localhost:3000](http://localhost:3000)

## 📦 Available Scripts

- `npm run dev` - Start all applications in development mode
- `npm run dev:frontend` - Start only the frontend application
- `npm run build` - Build all applications for production
- `npm run lint` - Run linting across all applications

## 🛠️ Technology Stack

### Frontend (`apps/frontend`)
- **Framework**: Next.js 15.5.4 with App Router
- **Language**: TypeScript
- **Styling**: CSS (ready for CSS modules, Tailwind, or styled-components)
- **Package Manager**: npm with workspaces

### Development Tools
- **Monorepo**: Turbo for build system and task running
- **TypeScript**: Strict configuration with path aliases
- **Linting**: ESLint (via Next.js)

## 🏃‍♂️ Development Workflow

### Adding New Components

Components should be placed in `apps/frontend/src/components/`:

```typescript
// apps/frontend/src/components/MyComponent.tsx
export default function MyComponent() {
  return <div>My Component</div>;
}
```

Import using the `@/` alias:

```typescript
import MyComponent from '@/components/MyComponent';
```

### Project Structure Guidelines

- **Components**: Place in `apps/frontend/src/components/`
- **Pages**: Use Next.js App Router in `apps/frontend/src/app/`
- **Shared utilities**: Future packages will go in `packages/`
- **Types**: Place TypeScript types near their usage or in a shared types file

## 🔧 Configuration

### TypeScript

The project uses a hierarchical TypeScript configuration:
- Root `tsconfig.json` provides base configuration
- `apps/frontend/tsconfig.json` extends the root with Next.js-specific settings

### Path Aliases

The following path aliases are configured:
- `@/*` → `./src/*` (within frontend app)

## 🚀 Deployment

Build the project for production:

```bash
npm run build
```

The built files will be in `apps/frontend/.next/`

## 📝 Contributing

1. Create a feature branch from `main`
2. Make your changes
3. Run `npm run lint` to check for issues
4. Run `npm run build` to ensure everything builds
5. Submit a pull request

## 📄 License

[Add your license information here]

## 🤝 Support

[Add support/contact information here]